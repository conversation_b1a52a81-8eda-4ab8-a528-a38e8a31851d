{% extends 'admin/base.html.twig' %}

{% block title %}Onsite Course Details - Capitol Academy Admin{% endblock %}

{% block page_title %}Onsite Course Details{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item"><a href="{{ path('admin_onsite_courses') }}">Onsite Courses</a></li>
<li class="breadcrumb-item active">{{ course.title }}</li>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class="card border-0 shadow-lg mb-4">
        <div class="card-header" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2 class="card-title mb-0" style="font-size: 1.8rem; font-weight: 600;">
                        <i class="fas fa-graduation-cap mr-3" style="font-size: 2rem;"></i>
                        Onsite Course Details: {{ course.code }}
                    </h2>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end align-items-center flex-wrap">
                        <!-- Edit Onsite Course Button -->
                        <a href="{{ path('admin_onsite_course_edit', {'code': course.code}) }}"
                           class="btn me-2 mb-2 mb-md-0"
                           style="font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: #007bff; color: white; border: 2px solid #007bff; transition: all 0.3s ease;"
                           onmouseover="this.style.background='#0056b3';"
                           onmouseout="this.style.background='#007bff';">
                            <i class="fas fa-edit me-2"></i>
                            Edit
                        </a>

                        <!-- Toggle Status Button -->
                        <button type="button"
                                class="btn me-2 mb-2 mb-md-0"
                                style="font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: {{ course.isActive ? '#6c757d' : '#28a745' }}; color: white; border: 2px solid {{ course.isActive ? '#6c757d' : '#28a745' }}; transition: all 0.3s ease;"
                                onclick="toggleOnsiteCourseStatus({{ course.id }}, '{{ course.title }}', {{ course.isActive ? 'true' : 'false' }})">
                            <i class="fas fa-{{ course.isActive ? 'pause' : 'play' }} me-2"></i>
                            {{ course.isActive ? 'Deactivate' : 'Activate' }}
                        </button>

                        <!-- Back to Onsite Courses Button -->
                        <a href="{{ path('admin_onsite_courses') }}"
                           class="btn mb-2 mb-md-0"
                           style="font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;"
                           onmouseover="this.style.background='#011a2d'; this.style.color='white';"
                           onmouseout="this.style.background='white'; this.style.color='#011a2d';">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Onsite Courses
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="card-body">
            <div class="row">
                <!-- Left Column - Course Information -->
                <div class="col-lg-8">
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-12 mb-4">
                            <h4 class="text-primary mb-3">
                                <i class="fas fa-info-circle me-2"></i>Basic Information
                            </h4>
                            <div class="table-responsive">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-bold" style="width: 150px;">Course Code:</td>
                                        <td><span class="badge bg-primary fs-6">{{ course.code }}</span></td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Title:</td>
                                        <td class="fs-5 fw-semibold">{{ course.title }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Category:</td>
                                        <td>{{ course.category ?: '<span class="text-muted">Not specified</span>' }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Level:</td>
                                        <td>{{ course.level ?: '<span class="text-muted">Not specified</span>' }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Duration:</td>
                                        <td>{{ course.duration ? (course.duration ~ ' minutes') : '<span class="text-muted">Not specified</span>' }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Price:</td>
                                        <td class="fs-5 text-success fw-bold">${{ course.price }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Status:</td>
                                        <td>
                                            {% if course.isActive %}
                                                <span class="badge bg-success fs-6">Active</span>
                                            {% else %}
                                                <span class="badge bg-secondary fs-6">Inactive</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Modules:</td>
                                        <td>
                                            {% if course.hasModules %}
                                                <span class="badge bg-info fs-6">Enabled</span>
                                            {% else %}
                                                <span class="badge bg-secondary fs-6">Disabled</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <!-- Description -->
                        {% if course.description %}
                        <div class="col-12 mb-4">
                            <h4 class="text-primary mb-3">
                                <i class="fas fa-align-left me-2"></i>Description
                            </h4>
                            <div class="p-3 bg-light rounded">
                                {{ course.description|nl2br }}
                            </div>
                        </div>
                        {% endif %}

                        <!-- Learning Outcomes -->
                        {% if course.learningOutcomes and course.learningOutcomes|length > 0 %}
                        <div class="col-12 mb-4">
                            <h4 class="text-primary mb-3">
                                <i class="fas fa-bullseye me-2"></i>Learning Outcomes
                            </h4>
                            <ul class="list-group list-group-flush">
                                {% for outcome in course.learningOutcomes %}
                                    <li class="list-group-item d-flex align-items-start">
                                        <i class="fas fa-check-circle text-success me-2 mt-1"></i>
                                        {{ outcome }}
                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}

                        <!-- Features -->
                        {% if course.features and course.features|length > 0 %}
                        <div class="col-12 mb-4">
                            <h4 class="text-primary mb-3">
                                <i class="fas fa-star me-2"></i>Features
                            </h4>
                            <ul class="list-group list-group-flush">
                                {% for feature in course.features %}
                                    <li class="list-group-item d-flex align-items-start">
                                        <i class="fas fa-star text-warning me-2 mt-1"></i>
                                        {{ feature }}
                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Right Column - Images and Statistics -->
                <div class="col-lg-4">
                    <!-- Thumbnail Image -->
                    {% if course.thumbnailImage %}
                    <div class="mb-4">
                        <h5 class="text-primary mb-3">
                            <i class="fas fa-image me-2"></i>Thumbnail Image
                        </h5>
                        <img src="{{ course.thumbnailUrl }}" alt="{{ course.title }}" class="img-fluid rounded shadow">
                    </div>
                    {% endif %}

                    <!-- Banner Image -->
                    {% if course.bannerImage %}
                    <div class="mb-4">
                        <h5 class="text-primary mb-3">
                            <i class="fas fa-panorama me-2"></i>Banner Image
                        </h5>
                        <img src="/uploads/courses/{{ course.bannerImage }}" alt="{{ course.title }}" class="img-fluid rounded shadow">
                    </div>
                    {% endif %}

                    <!-- Statistics -->
                    <div class="mb-4">
                        <h5 class="text-primary mb-3">
                            <i class="fas fa-chart-bar me-2"></i>Statistics
                        </h5>
                        <div class="card">
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6 mb-3">
                                        <div class="text-primary fs-4 fw-bold">{{ course.viewCount }}</div>
                                        <small class="text-muted">Views</small>
                                    </div>
                                    <div class="col-6 mb-3">
                                        <div class="text-success fs-4 fw-bold">{{ course.enrolledCount }}</div>
                                        <small class="text-muted">Enrolled</small>
                                    </div>
                                    <div class="col-6">
                                        <div class="text-info fs-4 fw-bold">{{ course.activeEnrollments }}</div>
                                        <small class="text-muted">Active</small>
                                    </div>
                                    <div class="col-6">
                                        <div class="text-warning fs-4 fw-bold">{{ course.completedCount }}</div>
                                        <small class="text-muted">Completed</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Timestamps -->
                    <div class="mb-4">
                        <h5 class="text-primary mb-3">
                            <i class="fas fa-clock me-2"></i>Timestamps
                        </h5>
                        <div class="card">
                            <div class="card-body">
                                <p class="mb-2">
                                    <strong>Created:</strong><br>
                                    <small class="text-muted">{{ course.createdAt|date('F j, Y \\a\\t g:i A') }}</small>
                                </p>
                                <p class="mb-0">
                                    <strong>Course Status:</strong><br>
                                    <small class="text-muted">{{ course.isActive ? 'Active' : 'Inactive' }}</small>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleOnsiteCourseStatus(courseId, courseTitle, currentStatus) {
    const action = currentStatus ? 'deactivate' : 'activate';
    if (confirm(`Are you sure you want to ${action} the onsite course "${courseTitle}"?`)) {
        fetch(`/admin/onsite-courses/${courseId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                redirect_to: 'preview'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the onsite course status.');
        });
    }
}
</script>
{% endblock %}
